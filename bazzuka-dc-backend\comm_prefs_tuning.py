"""
Communication Preferences Agent <PERSON><PERSON>

This script helps tune the comm prefs agent to eliminate false positives by:
1. Testing different model configurations
2. Adjusting temperature settings
3. Modifying prompts
4. Running validation tests

Usage:
    python comm_prefs_tuning.py --test-current
    python comm_prefs_tuning.py --tune-model gpt-4.1
    python comm_prefs_tuning.py --tune-temperature 0.0
    python comm_prefs_tuning.py --validate
"""

import argparse
import json
import sys
from typing import List, Dict, Any
from unittest.mock import patch, MagicMock
from test_comm_prefs_false_positives import run_false_positive_detection
from app.core.ai.comm_prefs_agent import analyze_communication_preferences, comm_preferences_client


class CommPrefsTuner:
    """Main class for tuning the communication preferences agent."""
    
    def __init__(self):
        self.test_conversations = [
            {
                "name": "normal_conversation",
                "conversation": """
                AI: Hi, this is <PERSON><PERSON> calling about your outstanding balance. How are you today?
                User: I'm doing okay, thanks for asking.
                AI: Great! I wanted to discuss your account and see if we can work out a payment plan.
                User: Sure, I'd like to get this resolved. What are my options?
                AI: We have several payment plan options available. Would you prefer weekly or monthly payments?
                User: Monthly would work better for me.
                """,
                "should_trigger_tools": False
            },
            {
                "name": "rude_no_opt_out",
                "conversation": """
                AI: Hi, this is Debby from collections regarding your account.
                User: Ugh, not again. This is so annoying.
                AI: I understand this might be frustrating. I'm here to help resolve this.
                User: Whatever, just tell me what you want.
                AI: I'd like to discuss payment options for your outstanding balance.
                User: Fine, but make it quick. I don't have all day.
                """,
                "should_trigger_tools": False
            },
            {
                "name": "schedule_followup",
                "conversation": """
                AI: Hi, this is Debby calling about your account.
                User: Hi Debby, I can't talk right now. I'm at work.
                AI: I understand. When would be a better time to reach you?
                User: Can you call me back tomorrow evening around 7 PM?
                AI: Absolutely! I'll call you tomorrow at 7 PM.
                User: Thanks, that works much better for me.
                """,
                "should_trigger_tools": False
            },
            {
                "name": "voicemail_scenario",
                "conversation": """
                AI: Hi, this is Debby calling about your outstanding balance. Please call us back at 555-0123.
                [Voicemail - no response from user]
                """,
                "should_trigger_tools": False
            },
            {
                "name": "busy_cooperative",
                "conversation": """
                AI: Hi, this is Debby calling about your account.
                User: Hi, I'm really busy right now but I know I need to deal with this.
                AI: I appreciate you taking the time. Would you prefer if I called back later?
                User: Yes, that would be great. Maybe this weekend?
                AI: Sure, I can call you this Saturday morning.
                User: Perfect, thank you for being understanding.
                """,
                "should_trigger_tools": False
            },
            {
                "name": "explicit_opt_out_calls",
                "conversation": """
                AI: Hi, this is Debby calling about your account.
                User: Please stop calling me. I don't want any more phone calls.
                AI: I understand. I'll make sure to note that in your account.
                User: Thank you.
                """,
                "should_trigger_tools": True  # This SHOULD trigger tools
            },
            {
                "name": "explicit_time_restriction",
                "conversation": """
                AI: Hi, this is Debby calling about your account.
                User: I can talk now, but please only call me after 6 PM in the future.
                AI: I'll make note of that preference.
                User: Thank you, I appreciate it.
                """,
                "should_trigger_tools": True  # This SHOULD trigger tools
            }
        ]
    
    def test_current_configuration(self) -> Dict[str, Any]:
        """Test the current configuration and return results."""
        print("🔍 Testing current configuration...")
        
        results = {
            "total_tests": len(self.test_conversations),
            "false_positives": [],
            "false_negatives": [],
            "correct_predictions": 0
        }
        
        for test_case in self.test_conversations:
            tool_calls_made = self._analyze_conversation_with_mock(test_case["conversation"])
            
            tools_were_called = len(tool_calls_made) > 0
            should_call_tools = test_case["should_trigger_tools"]
            
            if tools_were_called and not should_call_tools:
                # False positive - tools called when they shouldn't be
                results["false_positives"].append({
                    "test_name": test_case["name"],
                    "conversation": test_case["conversation"],
                    "tool_calls": tool_calls_made
                })
            elif not tools_were_called and should_call_tools:
                # False negative - tools not called when they should be
                results["false_negatives"].append({
                    "test_name": test_case["name"],
                    "conversation": test_case["conversation"]
                })
            else:
                # Correct prediction
                results["correct_predictions"] += 1
        
        return results
    
    def _analyze_conversation_with_mock(self, conversation: str) -> List[Dict]:
        """Analyze conversation and return tool calls made."""
        tool_calls_made = []
        
        def mock_execute(func_name, args):
            tool_calls_made.append({"function": func_name, "args": args})
            return {"status": "mocked"}
        
        with patch.object(comm_preferences_client.actions['communication_preferences_analysis'].tool_engine, 
                        'execute', side_effect=mock_execute):
            try:
                analyze_communication_preferences(conversation, "test_defaulter")
            except Exception as e:
                print(f"Error analyzing conversation: {e}")
        
        return tool_calls_made
    
    def test_with_model(self, model_name: str) -> Dict[str, Any]:
        """Test with a different model."""
        print(f"🤖 Testing with model: {model_name}")
        
        # Temporarily change the model
        original_model = comm_preferences_client.model_name
        comm_preferences_client.model_name = model_name
        
        try:
            results = self.test_current_configuration()
            results["model_used"] = model_name
            return results
        finally:
            # Restore original model
            comm_preferences_client.model_name = original_model
    
    def test_with_temperature(self, temperature: float) -> Dict[str, Any]:
        """Test with a different temperature setting."""
        print(f"🌡️ Testing with temperature: {temperature}")
        
        # Note: This would require modifying the AIClient to accept temperature
        # For now, we'll simulate this by patching the OpenAI call
        def mock_create_with_temp(*args, **kwargs):
            kwargs['temperature'] = temperature
            return comm_preferences_client.model.chat.completions.create(*args, **kwargs)
        
        with patch.object(comm_preferences_client.model.chat.completions, 'create', side_effect=mock_create_with_temp):
            results = self.test_current_configuration()
            results["temperature_used"] = temperature
            return results
    
    def print_results(self, results: Dict[str, Any]):
        """Print test results in a readable format."""
        print(f"\n📊 RESULTS SUMMARY")
        print(f"Total tests: {results['total_tests']}")
        print(f"Correct predictions: {results['correct_predictions']}")
        print(f"False positives: {len(results['false_positives'])}")
        print(f"False negatives: {len(results['false_negatives'])}")
        
        if 'model_used' in results:
            print(f"Model: {results['model_used']}")
        if 'temperature_used' in results:
            print(f"Temperature: {results['temperature_used']}")
        
        accuracy = results['correct_predictions'] / results['total_tests'] * 100
        print(f"Accuracy: {accuracy:.1f}%")
        
        if results['false_positives']:
            print(f"\n❌ FALSE POSITIVES ({len(results['false_positives'])}):")
            for fp in results['false_positives']:
                print(f"  - {fp['test_name']}: {fp['tool_calls']}")
        
        if results['false_negatives']:
            print(f"\n⚠️ FALSE NEGATIVES ({len(results['false_negatives'])}):")
            for fn in results['false_negatives']:
                print(f"  - {fn['test_name']}")
        
        if not results['false_positives'] and not results['false_negatives']:
            print("✅ Perfect score! No false positives or negatives.")
    
    def run_validation_suite(self) -> bool:
        """Run additional validation conversations with Debby-style interactions."""
        print("🧪 Running validation suite...")
        
        validation_conversations = [
            "Hi Debby, I'm having a tough time financially right now.",
            "This is really stressful for me to deal with.",
            "I don't have the money right now, but I will soon.",
            "Can we work out some kind of payment plan?",
            "I'm sorry, I'm at work and can't talk long.",
            "Let me call you back when I have more time.",
            "I appreciate you working with me on this.",
            "Thank you for being understanding about my situation."
        ]
        
        false_positives_found = 0
        
        for i, user_response in enumerate(validation_conversations):
            conversation = f"""
            AI: Hi, this is Debby calling about your outstanding account balance.
            User: {user_response}
            AI: I understand. Let me see how I can help you with this.
            """
            
            tool_calls = self._analyze_conversation_with_mock(conversation)
            if len(tool_calls) > 0:
                false_positives_found += 1
                print(f"❌ Validation {i+1}: False positive detected")
                print(f"   User said: '{user_response}'")
                print(f"   Tools called: {tool_calls}")
        
        print(f"\n📈 Validation Results:")
        print(f"Total validation tests: {len(validation_conversations)}")
        print(f"False positives found: {false_positives_found}")
        
        return false_positives_found == 0
    
    def suggest_improvements(self, results: Dict[str, Any]):
        """Suggest improvements based on test results."""
        print(f"\n💡 IMPROVEMENT SUGGESTIONS:")

        if results['false_positives']:
            print("To reduce false positives:")
            print("1. Set temperature to 0.0 for more deterministic responses")
            print("2. Strengthen the prompt with more explicit examples of what NOT to trigger on")
            print("3. Try GPT-4.1 instead of GPT-4.1-mini for better instruction following")
            print("4. Add negative examples to the prompt")
            print("5. Use stronger language like 'ONLY if explicitly requested'")

        if results['false_negatives']:
            print("To reduce false negatives:")
            print("1. Add more explicit examples of opt-out language")
            print("2. Improve pattern recognition for restriction requests")

        if not results['false_positives'] and not results['false_negatives']:
            print("✅ Configuration looks good! Consider running validation suite.")

    def generate_improved_prompt(self, results: Dict[str, Any]) -> str:
        """Generate an improved prompt based on false positive patterns."""
        base_prompt = """Analyze the conversation transcript between a debt collector and a debtor to identify any explicit requests related to communication preferences.

        **CRITICAL: Only act on EXPLICIT, DIRECT requests. Do NOT infer or assume preferences.**

        - If—and only if—the debtor **explicitly and directly** asks to stop receiving messages entirely through a specific channel (e.g., call, email, text), call the `opt_out_of_communications` tool with the appropriate channel.
            - You must always respect and act on any clear opt-out request.
            - Example phrases include: "Please stop calling me," "Don't email me anymore," or "I no longer want to receive texts."
        - If the debtor **explicitly and directly** specifies limits on when they can be contacted (e.g., only during certain hours, days of the week, or not on holidays), call the `restrict_communications` tool with the relevant restrictions.
            - Example phrases include: "Only call me after 5pm," "Do not contact me on weekends," or "Never call me at work."

        **DO NOT trigger tools for these scenarios:**
        - Being busy or asking to call back later
        - Expressing frustration or annoyance without requesting restrictions
        - Voicemail or no response
        - Scheduling follow-up calls
        - General complaints about timing without specific restrictions
        - Saying "I can't talk right now" without explicit opt-out language
        - Being rude or short without requesting communication changes

        **Examples of what should NOT trigger tools:**
        - "I'm busy right now" → NO TOOLS
        - "This is annoying" → NO TOOLS
        - "Can you call me back later?" → NO TOOLS
        - "I don't have time for this" → NO TOOLS
        - "Call me tomorrow at 7 PM" → NO TOOLS

        Only act if the debtor makes a clear and affirmative request related to communication channels or timing.
        """

        if results['false_positives']:
            # Add specific examples from false positives
            fp_examples = "\n**Additional examples that should NOT trigger tools:**\n"
            for fp in results['false_positives'][:3]:  # Add up to 3 examples
                conversation_snippet = fp['conversation'].strip()[:200] + "..."
                fp_examples += f"- Scenario: {conversation_snippet} → NO TOOLS\n"

            base_prompt += fp_examples

        return base_prompt


def main():
    parser = argparse.ArgumentParser(description="Tune communication preferences agent")
    parser.add_argument("--test-current", action="store_true", help="Test current configuration")
    parser.add_argument("--tune-model", type=str, help="Test with different model (e.g., gpt-4.1)")
    parser.add_argument("--tune-temperature", type=float, help="Test with different temperature")
    parser.add_argument("--validate", action="store_true", help="Run validation suite")
    parser.add_argument("--suggest", action="store_true", help="Get improvement suggestions")
    
    args = parser.parse_args()
    
    tuner = CommPrefsTuner()
    
    if args.test_current:
        results = tuner.test_current_configuration()
        tuner.print_results(results)
        
        if args.suggest:
            tuner.suggest_improvements(results)
    
    elif args.tune_model:
        results = tuner.test_with_model(args.tune_model)
        tuner.print_results(results)
    
    elif args.tune_temperature is not None:
        results = tuner.test_with_temperature(args.tune_temperature)
        tuner.print_results(results)
    
    elif args.validate:
        success = tuner.run_validation_suite()
        if success:
            print("✅ Validation passed!")
            sys.exit(0)
        else:
            print("❌ Validation failed!")
            sys.exit(1)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
