# Communication Preferences Agent - False Positive Testing Framework

This testing framework helps identify and eliminate false positives in the communication preferences agent, where the agent incorrectly calls restriction tools when users don't actually request opt-outs or communication restrictions.

## Problem Statement

The comm prefs agent currently has two behavioral patterns:
- ✅ **True Positives Work Well**: Correctly identifies when users explicitly request opt-outs or restrictions
- ❌ **False Positives Problem**: Sometimes calls restriction tools when users don't actually request them

## Files Overview

### Core Testing Files

1. **`test_comm_prefs_false_positives.py`** - Main test suite with pytest-compatible tests
2. **`comm_prefs_tuning.py`** - Configuration tuning and testing different model settings
3. **`run_comm_prefs_tests.py`** - Simple script to run tests and apply improvements
4. **`COMM_PREFS_TESTING_README.md`** - This documentation

### Target Files

- **`app/core/ai/comm_prefs_agent.py`** - The main agent implementation
- **`app/core/ai/comm_prefs_tools.py`** - Tool definitions for opt-out and restrictions

## Quick Start

### 1. Run Initial Test

```bash
cd bazzuka-dc-backend
python run_comm_prefs_tests.py --quick
```

This will quickly show you if there are any false positives with the current configuration.

### 2. Full Testing Suite

```bash
python run_comm_prefs_tests.py
```

This runs the complete testing suite and offers interactive improvements.

### 3. Specific Testing Options

```bash
# Test current configuration
python comm_prefs_tuning.py --test-current

# Test with different model
python comm_prefs_tuning.py --tune-model gpt-4.1

# Test with temperature 0 for reproducibility
python comm_prefs_tuning.py --tune-temperature 0.0

# Run validation suite
python comm_prefs_tuning.py --validate

# Get improvement suggestions
python comm_prefs_tuning.py --test-current --suggest
```

### 4. Run Pytest

```bash
pytest test_comm_prefs_false_positives.py -v
```

## Test Cases

### False Positive Scenarios (Should NOT trigger tools)

1. **Normal Conversation** - Regular payment discussion
2. **Rude Response** - User is annoyed but doesn't request opt-out
3. **Scheduling Follow-up** - User asks to be called back later
4. **Voicemail** - No response from user
5. **Busy but Cooperative** - User is busy but willing to work with collector
6. **Information Requests** - User asking for account details
7. **Expressing Frustration** - User stressed but not requesting restrictions
8. **Wrong Number** - Polite wrong number scenario
9. **Payment Discussion** - Normal payment planning conversation

### True Positive Scenarios (SHOULD trigger tools)

1. **Explicit Opt-out** - "Please stop calling me"
2. **Time Restrictions** - "Only call me after 6 PM"
3. **Channel Restrictions** - "Don't email me anymore"

## Current Configuration

- **Model**: `gpt-4.1-mini`
- **Temperature**: Default (not explicitly set)
- **Tools**: `opt_out_of_communications`, `restrict_communications`

## Tuning Process

### Step 1: Identify False Positives

Run the test suite to identify current false positive rate:

```bash
python comm_prefs_tuning.py --test-current
```

### Step 2: Try Different Configurations

**Model Tuning:**
```bash
# Try GPT-4.1 for better instruction following
python comm_prefs_tuning.py --tune-model gpt-4.1
```

**Temperature Tuning:**
```bash
# Set temperature to 0 for reproducibility
python comm_prefs_tuning.py --tune-temperature 0.0
```

### Step 3: Prompt Engineering

The framework can automatically generate improved prompts based on false positive patterns:

```bash
python run_comm_prefs_tests.py
# Follow prompts to apply improved prompt
```

### Step 4: Validation

After making changes, run the validation suite:

```bash
python comm_prefs_tuning.py --validate
```

## Expected Outcomes

### Success Criteria

- **Zero false positives** on the test suite
- **All true positives** still work correctly
- **Validation suite passes** with additional conversation scenarios

### Improvement Strategies

1. **Temperature = 0.0** - More deterministic responses
2. **GPT-4.1 vs GPT-4.1-mini** - Better instruction following
3. **Enhanced Prompts** - More explicit negative examples
4. **Stronger Language** - "ONLY if explicitly requested"

## Monitoring and Maintenance

### Regular Testing

Run the test suite regularly to catch regressions:

```bash
# Quick daily check
python run_comm_prefs_tests.py --quick

# Weekly full validation
python comm_prefs_tuning.py --validate
```

### Adding New Test Cases

When you find new false positive scenarios, add them to `test_comm_prefs_false_positives.py`:

```python
def test_new_scenario(self):
    """Test description of new scenario."""
    conversation = """
    AI: Hi, this is Debby...
    User: [new problematic response]
    """
    self._analyze_and_check_no_tools(conversation)
```

### Performance Metrics

Track these metrics over time:
- False positive rate (target: 0%)
- True positive rate (target: 100%)
- Overall accuracy
- Response consistency (with temperature 0)

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're in the `bazzuka-dc-backend` directory
2. **Module Not Found**: Check that all dependencies are installed
3. **Test Failures**: Review the specific conversation that caused the false positive

### Debug Mode

Add debug prints to see what the agent is thinking:

```python
# In comm_prefs_agent.py, add logging to see the full response
print(f"[DEBUG] Agent response: {result}")
```

## Integration with Development Workflow

### Before Deploying Changes

1. Run the full test suite
2. Ensure zero false positives
3. Validate with additional conversations
4. Document any prompt changes

### Continuous Integration

Consider adding these tests to your CI pipeline:

```bash
# In CI script
python comm_prefs_tuning.py --validate
if [ $? -ne 0 ]; then
    echo "Communication preferences tests failed"
    exit 1
fi
```

## Next Steps

1. **Collect Real False Positives**: Have conversations with Debby to find actual false positive cases
2. **Add to Test Suite**: Include real-world scenarios in the test cases
3. **Automate Tuning**: Create scripts to automatically try different configurations
4. **Monitor Production**: Set up alerts for unexpected tool usage patterns

## Support

For questions or issues with this testing framework, check:
1. The test output for specific error messages
2. The agent logs for debugging information
3. The current prompt in `comm_prefs_agent.py`
