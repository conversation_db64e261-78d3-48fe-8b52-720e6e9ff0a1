"""
Test suite for detecting false positives in the communication preferences agent.

This script tests scenarios where the agent should NOT call restriction tools,
but currently does (false positives). The goal is to eliminate these false positives
through prompt engineering, model tuning, or temperature adjustments.
"""

import pytest
import json
from unittest.mock import patch, MagicMock
from app.core.ai.comm_prefs_agent import analyze_communication_preferences, comm_preferences_client
from app.ai.utils import get_tool_calls


class TestCommPrefsFalsePositives:
    """Test cases for false positive scenarios where restriction tools should NOT be called."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.defaulter_id = "test_defaulter_123"
        
    def _analyze_and_check_no_tools(self, conversation: str) -> dict:
        """
        Helper method to analyze conversation and assert no tools were called.
        Returns the analysis result for further inspection.
        """
        # Mock the tool execution to capture calls without actually executing them
        with patch('app.core.payments.PaymentsTool') as mock_payments_tool:
            mock_instance = MagicMock()
            mock_payments_tool.return_value = mock_instance
            
            # Track tool calls
            tool_calls_made = []
            
            def mock_execute(func_name, args):
                tool_calls_made.append({"function": func_name, "args": args})
                return {"status": "mocked"}
            
            # Patch the tool engine execute method
            with patch.object(comm_preferences_client.actions['communication_preferences_analysis'].tool_engine, 
                            'execute', side_effect=mock_execute):
                
                result = analyze_communication_preferences(conversation, self.defaulter_id)
                
                # Assert no tools were called (this is what we want for these test cases)
                assert len(tool_calls_made) == 0, f"Expected no tool calls, but got: {tool_calls_made}"
                
                return result

    def test_normal_conversation_with_debby(self):
        """Test normal conversation that should not trigger any restrictions."""
        conversation = """
        AI: Hi, this is Debby calling about your outstanding balance. How are you today?
        User: I'm doing okay, thanks for asking.
        AI: Great! I wanted to discuss your account and see if we can work out a payment plan.
        User: Sure, I'd like to get this resolved. What are my options?
        AI: We have several payment plan options available. Would you prefer weekly or monthly payments?
        User: Monthly would work better for me.
        AI: Perfect! I can set up a monthly payment plan for you.
        """
        self._analyze_and_check_no_tools(conversation)

    def test_rude_response_no_opt_out_request(self):
        """Test rude/annoyed response that doesn't explicitly request opt-out."""
        conversation = """
        AI: Hi, this is Debby from collections regarding your account.
        User: Ugh, not again. This is so annoying.
        AI: I understand this might be frustrating. I'm here to help resolve this.
        User: Whatever, just tell me what you want.
        AI: I'd like to discuss payment options for your outstanding balance.
        User: Fine, but make it quick. I don't have all day.
        """
        self._analyze_and_check_no_tools(conversation)

    def test_scheduling_follow_up_call(self):
        """Test scheduling a follow-up call - this should NOT trigger restrictions."""
        conversation = """
        AI: Hi, this is Debby calling about your account.
        User: Hi Debby, I can't talk right now. I'm at work.
        AI: I understand. When would be a better time to reach you?
        User: Can you call me back tomorrow evening around 7 PM?
        AI: Absolutely! I'll call you tomorrow at 7 PM.
        User: Thanks, that works much better for me.
        """
        self._analyze_and_check_no_tools(conversation)

    def test_voicemail_no_explicit_request(self):
        """Test voicemail scenario - should not trigger restrictions."""
        conversation = """
        AI: Hi, this is Debby calling about your outstanding balance. Please call us back at 555-0123.
        [Voicemail - no response from user]
        """
        self._analyze_and_check_no_tools(conversation)

    def test_busy_but_cooperative(self):
        """Test user who is busy but cooperative - no opt-out request."""
        conversation = """
        AI: Hi, this is Debby calling about your account.
        User: Hi, I'm really busy right now but I know I need to deal with this.
        AI: I appreciate you taking the time. Would you prefer if I called back later?
        User: Yes, that would be great. Maybe this weekend?
        AI: Sure, I can call you this Saturday morning.
        User: Perfect, thank you for being understanding.
        """
        self._analyze_and_check_no_tools(conversation)

    def test_asking_for_information_only(self):
        """Test user asking for information without requesting restrictions."""
        conversation = """
        AI: Hi, this is Debby calling about your account balance.
        User: Hi Debby. Can you tell me exactly how much I owe?
        AI: Your current balance is $1,250.
        User: And when was this originally due?
        AI: The original due date was March 15th.
        User: Okay, let me think about this and get back to you.
        """
        self._analyze_and_check_no_tools(conversation)

    def test_expressing_frustration_no_opt_out(self):
        """Test user expressing frustration but not requesting to opt out."""
        conversation = """
        AI: Hi, this is Debby calling about your overdue payment.
        User: This is really stressful. I'm dealing with a lot right now.
        AI: I understand this can be overwhelming. We're here to help find a solution.
        User: I just wish I had more time to figure this out.
        AI: We can definitely work with you on timing. What would help?
        User: Maybe if I could pay in smaller amounts over time?
        """
        self._analyze_and_check_no_tools(conversation)

    def test_wrong_number_polite_response(self):
        """Test wrong number scenario - should not trigger restrictions."""
        conversation = """
        AI: Hi, this is Debby calling for John Smith about an outstanding balance.
        User: I think you have the wrong number. I'm not John Smith.
        AI: I apologize for the confusion. Can you confirm the number I reached?
        User: This is 555-0199.
        AI: Thank you, I'll update our records. Sorry for bothering you.
        User: No problem, have a good day.
        """
        self._analyze_and_check_no_tools(conversation)

    def test_payment_discussion_no_restrictions(self):
        """Test payment discussion without any restriction requests."""
        conversation = """
        AI: Hi, this is Debby calling about your account.
        User: Hi Debby, I've been expecting your call.
        AI: Great! I wanted to discuss your payment options.
        User: I can make a payment next Friday when I get paid.
        AI: That sounds good. What amount were you thinking?
        User: I can do $200 next Friday and then $200 the following Friday.
        AI: Perfect! I'll set that up for you.
        """
        self._analyze_and_check_no_tools(conversation)


class TestCommPrefsConfiguration:
    """Test different model configurations to reduce false positives."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.defaulter_id = "test_defaulter_456"
        self.false_positive_conversation = """
        AI: Hi, this is Debby calling about your account.
        User: I'm really busy right now.
        AI: I understand. When would be a better time?
        User: I don't know, I'll call you back.
        """
    
    @patch('app.core.ai.comm_prefs_agent.model', 'gpt-4.1')
    def test_with_gpt_4_1(self):
        """Test with GPT-4.1 model instead of mini."""
        # This test would need to be run manually to compare results
        pass
    
    def test_with_temperature_zero(self):
        """Test with temperature set to 0 for reproducibility."""
        # This would require modifying the client to accept temperature parameter
        pass


def run_false_positive_detection():
    """
    Main function to run false positive detection.
    Returns a report of any false positives found.
    """
    test_cases = [
        ("Normal conversation", """
        AI: Hi, this is Debby calling about your outstanding balance. How are you today?
        User: I'm doing okay, thanks for asking.
        AI: Great! I wanted to discuss your account and see if we can work out a payment plan.
        User: Sure, I'd like to get this resolved. What are my options?
        """),
        
        ("Rude but no opt-out", """
        AI: Hi, this is Debby from collections.
        User: Ugh, not again. This is so annoying.
        AI: I understand this might be frustrating.
        User: Whatever, just tell me what you want.
        """),
        
        ("Scheduling follow-up", """
        AI: Hi, this is Debby calling about your account.
        User: I can't talk right now. Can you call me back tomorrow at 7 PM?
        AI: Absolutely! I'll call you tomorrow at 7 PM.
        """),
        
        ("Busy but cooperative", """
        AI: Hi, this is Debby calling about your account.
        User: I'm really busy but I know I need to deal with this.
        AI: Would you prefer if I called back later?
        User: Yes, maybe this weekend?
        """),
        
        ("Information request", """
        AI: Hi, this is Debby calling about your account balance.
        User: Can you tell me exactly how much I owe?
        AI: Your current balance is $1,250.
        User: Let me think about this and get back to you.
        """)
    ]
    
    false_positives = []
    
    for test_name, conversation in test_cases:
        try:
            # Track tool calls
            tool_calls_made = []
            
            def mock_execute(func_name, args):
                tool_calls_made.append({"function": func_name, "args": args})
                return {"status": "mocked"}
            
            with patch.object(comm_preferences_client.actions['communication_preferences_analysis'].tool_engine, 
                            'execute', side_effect=mock_execute):
                
                result = analyze_communication_preferences(conversation, "test_defaulter")
                
                if len(tool_calls_made) > 0:
                    false_positives.append({
                        "test_name": test_name,
                        "conversation": conversation,
                        "tool_calls": tool_calls_made
                    })
                    
        except Exception as e:
            print(f"Error in test '{test_name}': {e}")
    
    return false_positives


if __name__ == "__main__":
    # Run false positive detection
    false_positives = run_false_positive_detection()
    
    print(f"\n=== FALSE POSITIVE DETECTION RESULTS ===")
    print(f"Found {len(false_positives)} false positives:")
    
    for fp in false_positives:
        print(f"\n❌ FALSE POSITIVE: {fp['test_name']}")
        print(f"Tool calls made: {fp['tool_calls']}")
        print(f"Conversation: {fp['conversation'][:100]}...")
    
    if len(false_positives) == 0:
        print("✅ No false positives detected!")
    
    # Run pytest for formal testing
    print(f"\n=== RUNNING PYTEST ===")
    pytest.main([__file__, "-v"])
