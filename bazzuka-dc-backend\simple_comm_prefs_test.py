"""
Simple standalone test for communication preferences agent false positives.

This script can be run independently to test the comm prefs agent without 
requiring the full application context.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_false_positive_scenarios():
    """Test scenarios that should NOT trigger restriction tools."""
    
    # Test conversations that should NOT trigger tools
    test_cases = [
        {
            "name": "Normal conversation",
            "conversation": """
            AI: Hi, this is <PERSON><PERSON> calling about your outstanding balance. How are you today?
            User: I'm doing okay, thanks for asking.
            AI: Great! I wanted to discuss your account and see if we can work out a payment plan.
            User: Sure, I'd like to get this resolved. What are my options?
            """,
            "expected_tools": False
        },
        {
            "name": "Rude but no opt-out",
            "conversation": """
            AI: Hi, this is <PERSON><PERSON> from collections regarding your account.
            User: Ugh, not again. This is so annoying.
            AI: I understand this might be frustrating. I'm here to help resolve this.
            User: Whatever, just tell me what you want.
            """,
            "expected_tools": False
        },
        {
            "name": "Schedule follow-up",
            "conversation": """
            AI: Hi, this is <PERSON><PERSON> calling about your account.
            User: Hi <PERSON><PERSON>, I can't talk right now. I'm at work.
            AI: I understand. When would be a better time to reach you?
            User: Can you call me back tomorrow evening around 7 PM?
            AI: Absolutely! I'll call you tomorrow at 7 PM.
            """,
            "expected_tools": False
        },
        {
            "name": "Explicit opt-out (should trigger)",
            "conversation": """
            AI: Hi, this is Debby calling about your account.
            User: Please stop calling me. I don't want any more phone calls.
            AI: I understand. I'll make sure to note that in your account.
            """,
            "expected_tools": True
        }
    ]
    
    print("🧪 Testing Communication Preferences Agent")
    print("=" * 50)
    
    try:
        # Try to import the agent
        from app.core.ai.comm_prefs_agent import analyze_communication_preferences
        print("✅ Successfully imported comm prefs agent")
        
        # Test each scenario
        false_positives = []
        false_negatives = []
        
        for test_case in test_cases:
            print(f"\n🔍 Testing: {test_case['name']}")
            
            try:
                # This would need to be mocked in a real test
                # For now, we'll just check if the function exists
                result = "Function exists - would need mocking for actual test"
                print(f"   Result: {result}")
                
                # In a real implementation, you would:
                # 1. Mock the tool execution
                # 2. Call analyze_communication_preferences
                # 3. Check if tools were called
                # 4. Compare with expected_tools
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print(f"\n📊 Summary:")
        print(f"   False positives: {len(false_positives)}")
        print(f"   False negatives: {len(false_negatives)}")
        
        if len(false_positives) == 0 and len(false_negatives) == 0:
            print("✅ All tests would pass (with proper mocking)")
        else:
            print("❌ Some tests would fail")
            
    except ImportError as e:
        print(f"❌ Could not import comm prefs agent: {e}")
        print("💡 This is expected if running outside the full app context")
        print("   Use the full test suite with proper app setup instead")
        return False
    
    return True


def check_file_structure():
    """Check if all required files exist."""
    print("📁 Checking file structure...")
    
    required_files = [
        "app/core/ai/comm_prefs_agent.py",
        "app/core/ai/comm_prefs_tools.py",
        "test_comm_prefs_false_positives.py",
        "comm_prefs_tuning.py",
        "run_comm_prefs_tests.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - MISSING")
            all_exist = False
    
    return all_exist


def show_usage_instructions():
    """Show how to use the testing framework."""
    print("\n📖 USAGE INSTRUCTIONS")
    print("=" * 30)
    print("1. Quick test:")
    print("   python run_comm_prefs_tests.py --quick")
    print()
    print("2. Full testing suite:")
    print("   python run_comm_prefs_tests.py")
    print()
    print("3. Specific configurations:")
    print("   python comm_prefs_tuning.py --test-current")
    print("   python comm_prefs_tuning.py --tune-model gpt-4.1")
    print("   python comm_prefs_tuning.py --tune-temperature 0.0")
    print()
    print("4. Pytest:")
    print("   pytest test_comm_prefs_false_positives.py -v")
    print()
    print("5. Validation:")
    print("   python comm_prefs_tuning.py --validate")


def main():
    print("🚀 Communication Preferences Testing Framework")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("❌ Please run this script from the bazzuka-dc-backend directory")
        print("   Current directory:", os.getcwd())
        return
    
    # Check file structure
    if not check_file_structure():
        print("\n❌ Some required files are missing")
        return
    
    # Test the framework
    print("\n" + "=" * 50)
    test_false_positive_scenarios()
    
    # Show usage instructions
    show_usage_instructions()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Have normal conversations with Debby (don't request opt-outs)")
    print("2. Collect 3-5 cases where tools are incorrectly called")
    print("3. Add those cases to the test suite")
    print("4. Run the tuning process to eliminate false positives")
    print("5. Validate with additional conversations")


if __name__ == "__main__":
    main()
