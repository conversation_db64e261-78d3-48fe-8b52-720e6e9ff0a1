#!/usr/bin/env python3
"""
Quick test runner for communication preferences false positive detection.

This script provides a simple way to:
1. Test the current configuration
2. Apply improvements
3. Validate changes

Usage:
    python run_comm_prefs_tests.py
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from comm_prefs_tuning import CommPrefsTuner
from test_comm_prefs_false_positives import run_false_positive_detection


def main():
    print("🚀 Communication Preferences Agent - False Positive Testing")
    print("=" * 60)
    
    # Initialize tuner
    tuner = CommPrefsTuner()
    
    # Test current configuration
    print("\n1️⃣ Testing current configuration...")
    current_results = tuner.test_current_configuration()
    tuner.print_results(current_results)
    
    # If we have false positives, suggest improvements
    if current_results['false_positives']:
        print("\n2️⃣ Analyzing false positives...")
        tuner.suggest_improvements(current_results)
        
        # Generate improved prompt
        print("\n3️⃣ Generating improved prompt...")
        improved_prompt = tuner.generate_improved_prompt(current_results)
        
        print("📝 Suggested improved prompt:")
        print("-" * 40)
        print(improved_prompt[:500] + "..." if len(improved_prompt) > 500 else improved_prompt)
        print("-" * 40)
        
        # Ask user if they want to apply the improved prompt
        response = input("\n🤔 Would you like to apply this improved prompt? (y/n): ")
        if response.lower() == 'y':
            apply_improved_prompt(improved_prompt)
            
            # Test again with improved prompt
            print("\n4️⃣ Testing with improved prompt...")
            new_results = tuner.test_current_configuration()
            tuner.print_results(new_results)
            
            # Compare results
            print("\n📈 IMPROVEMENT COMPARISON:")
            print(f"Before: {len(current_results['false_positives'])} false positives")
            print(f"After:  {len(new_results['false_positives'])} false positives")
            
            improvement = len(current_results['false_positives']) - len(new_results['false_positives'])
            if improvement > 0:
                print(f"✅ Reduced false positives by {improvement}!")
            elif improvement == 0:
                print("➡️ No change in false positives")
            else:
                print(f"❌ False positives increased by {abs(improvement)}")
    
    else:
        print("\n✅ No false positives detected! Running validation suite...")
        success = tuner.run_validation_suite()
        
        if success:
            print("\n🎉 All tests passed! The agent is working correctly.")
        else:
            print("\n⚠️ Validation suite found issues. Consider additional tuning.")
    
    # Offer additional testing options
    print("\n🔧 Additional testing options:")
    print("1. Test with GPT-4.1 (instead of mini)")
    print("2. Test with temperature 0.0")
    print("3. Run extended validation")
    print("4. Exit")
    
    choice = input("\nSelect an option (1-4): ")
    
    if choice == "1":
        print("\n🤖 Testing with GPT-4.1...")
        gpt4_results = tuner.test_with_model("gpt-4.1")
        tuner.print_results(gpt4_results)
        
        print("\n📊 Model Comparison:")
        print(f"GPT-4.1-mini: {len(current_results['false_positives'])} false positives")
        print(f"GPT-4.1:      {len(gpt4_results['false_positives'])} false positives")
        
    elif choice == "2":
        print("\n🌡️ Testing with temperature 0.0...")
        temp_results = tuner.test_with_temperature(0.0)
        tuner.print_results(temp_results)
        
        print("\n📊 Temperature Comparison:")
        print(f"Default temp: {len(current_results['false_positives'])} false positives")
        print(f"Temp 0.0:     {len(temp_results['false_positives'])} false positives")
        
    elif choice == "3":
        print("\n🧪 Running extended validation...")
        tuner.run_validation_suite()
        
    print("\n👋 Testing complete!")


def apply_improved_prompt(improved_prompt: str):
    """Apply the improved prompt to the comm prefs agent."""
    try:
        # Read the current comm_prefs_agent.py file
        agent_file = Path("app/core/ai/comm_prefs_agent.py")
        
        if not agent_file.exists():
            print("❌ Could not find comm_prefs_agent.py file")
            return
        
        with open(agent_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the prompt in the CommunicationPreferencesPromptGenerator class
        start_marker = 'self.prompt = """'
        end_marker = '"""'
        
        start_idx = content.find(start_marker)
        if start_idx == -1:
            print("❌ Could not find prompt start marker")
            return
        
        start_idx += len(start_marker)
        end_idx = content.find(end_marker, start_idx)
        
        if end_idx == -1:
            print("❌ Could not find prompt end marker")
            return
        
        # Replace the prompt
        new_content = content[:start_idx] + improved_prompt + content[end_idx:]
        
        # Backup the original file
        backup_file = agent_file.with_suffix('.py.backup')
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Write the new content
        with open(agent_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ Applied improved prompt to {agent_file}")
        print(f"📁 Backup saved as {backup_file}")
        
    except Exception as e:
        print(f"❌ Error applying improved prompt: {e}")


def quick_test():
    """Run a quick test to see current false positive rate."""
    print("⚡ Quick False Positive Test")
    print("-" * 30)
    
    false_positives = run_false_positive_detection()
    
    if len(false_positives) == 0:
        print("✅ No false positives detected!")
        return True
    else:
        print(f"❌ Found {len(false_positives)} false positives:")
        for fp in false_positives:
            print(f"  - {fp['test_name']}")
        return False


if __name__ == "__main__":
    # Check if we're in the right directory
    if not Path("app/core/ai/comm_prefs_agent.py").exists():
        print("❌ Please run this script from the bazzuka-dc-backend directory")
        sys.exit(1)
    
    # Quick test option
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        quick_test()
    else:
        main()
