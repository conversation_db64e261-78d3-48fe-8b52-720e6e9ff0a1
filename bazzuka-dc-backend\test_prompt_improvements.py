#!/usr/bin/env python3
"""
Test script to verify the prompt improvements for the comm prefs agent.

This script tests the improved prompt with negative examples to ensure
false positives are reduced.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_improved_prompt():
    """Test the improved prompt with false positive scenarios."""
    
    print("🧪 Testing Improved Communication Preferences Prompt")
    print("=" * 60)
    
    # Test cases that should NOT trigger tools (false positive scenarios)
    false_positive_tests = [
        {
            "name": "Busy user - should NOT trigger tools",
            "conversation": """
            AI: Hi, this is <PERSON><PERSON> calling about your outstanding balance.
            User: I'm really busy right now, can you call me back later?
            AI: Of course, when would be a good time?
            User: Maybe tomorrow evening around 7 PM?
            """,
            "expected_tools": False
        },
        {
            "name": "Annoyed user - should NOT trigger tools", 
            "conversation": """
            AI: Hi, this is <PERSON><PERSON> from collections.
            User: Ugh, this is so annoying. I don't have time for this.
            AI: I understand this is frustrating.
            User: Whatever, just tell me what you want.
            """,
            "expected_tools": False
        },
        {
            "name": "Can't talk at work - should NOT trigger tools",
            "conversation": """
            AI: Hi, this is Debby calling about your account.
            User: I can't talk right now, I'm at work.
            AI: I understand. When would be better?
            User: I'll call you back when I get home.
            """,
            "expected_tools": False
        },
        {
            "name": "Explicit opt-out - SHOULD trigger tools",
            "conversation": """
            AI: Hi, this is Debby calling about your account.
            User: Please stop calling me. I don't want any more phone calls.
            AI: I understand, I'll make note of that.
            """,
            "expected_tools": True
        }
    ]
    
    print("📝 Current prompt improvements:")
    print("✅ Added CRITICAL warning at the top")
    print("✅ Added comprehensive negative examples section")
    print("✅ Set temperature to 0.0 for reproducibility")
    print("✅ Strengthened language about explicit requests only")
    
    print(f"\n🔍 Testing {len(false_positive_tests)} scenarios...")
    
    try:
        # Try to import and test the agent
        from app.core.ai.comm_prefs_agent import CommunicationPreferencesPromptGenerator
        
        # Create the prompt generator to see the improved prompt
        prompt_gen = CommunicationPreferencesPromptGenerator()
        
        print("\n📄 IMPROVED PROMPT PREVIEW:")
        print("-" * 40)
        # Show first 500 characters of the improved prompt
        prompt_preview = prompt_gen.prompt[:800] + "..." if len(prompt_gen.prompt) > 800 else prompt_gen.prompt
        print(prompt_preview)
        print("-" * 40)
        
        # Check if our improvements are in the prompt
        improvements_found = []
        if "CRITICAL:" in prompt_gen.prompt:
            improvements_found.append("✅ Critical warning added")
        if "DO NOT call any tools for these scenarios:" in prompt_gen.prompt:
            improvements_found.append("✅ Negative examples section added")
        if "I'm busy right now" in prompt_gen.prompt:
            improvements_found.append("✅ Specific false positive examples included")
        
        print(f"\n🎯 Improvements detected:")
        for improvement in improvements_found:
            print(f"   {improvement}")
        
        if len(improvements_found) == 3:
            print("\n✅ All prompt improvements successfully applied!")
        else:
            print(f"\n⚠️ Only {len(improvements_found)}/3 improvements detected")
        
        # Note about testing
        print(f"\n📋 Next steps to test effectiveness:")
        print("1. Have conversations with Debby using the scenarios above")
        print("2. Verify that busy/annoyed responses don't trigger tools")
        print("3. Confirm explicit opt-outs still work correctly")
        print("4. Run the full test suite: python run_comm_prefs_tests.py")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import comm prefs agent: {e}")
        print("💡 Make sure you're in the bazzuka-dc-backend directory")
        return False
    except Exception as e:
        print(f"❌ Error testing prompt: {e}")
        return False


def show_before_after():
    """Show the key differences between old and new prompt."""
    print("\n📊 KEY IMPROVEMENTS MADE:")
    print("=" * 40)
    
    print("🔴 BEFORE (Original Prompt Issues):")
    print("   - Lacked specific negative examples")
    print("   - No explicit guidance on common false positives")
    print("   - Temperature not set (non-deterministic)")
    print("   - Could be interpreted too liberally")
    
    print("\n🟢 AFTER (Improved Prompt):")
    print("   - Added CRITICAL warning at the top")
    print("   - Comprehensive list of scenarios that should NOT trigger tools")
    print("   - Temperature set to 0.0 for reproducible results")
    print("   - Explicit examples: 'I'm busy' → NO TOOLS")
    print("   - Stronger language: 'When in doubt, DO NOT call any tools'")
    
    print("\n🎯 Expected Impact:")
    print("   - Significantly reduced false positives")
    print("   - More consistent behavior")
    print("   - Better handling of common scenarios")


def main():
    print("🚀 Communication Preferences Prompt Improvement Test")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("app/core/ai/comm_prefs_agent.py").exists():
        print("❌ Please run this script from the bazzuka-dc-backend directory")
        print(f"   Current directory: {Path.cwd()}")
        return
    
    # Test the improved prompt
    success = test_improved_prompt()
    
    # Show before/after comparison
    show_before_after()
    
    if success:
        print("\n🎉 Prompt improvements successfully applied!")
        print("\n🔄 To test the actual effectiveness:")
        print("   1. Have normal conversations with Debby")
        print("   2. Be busy, annoyed, or ask for callbacks")
        print("   3. Verify no restriction tools are called")
        print("   4. Test explicit opt-outs still work")
    else:
        print("\n❌ There were issues with the prompt improvements")
    
    print(f"\n📚 For comprehensive testing, run:")
    print(f"   python run_comm_prefs_tests.py")


if __name__ == "__main__":
    main()
